# AR0234CS Camera Integration Guide

## Overview

The VisionAngel Pod now includes a production-ready driver for the OnSemi AR0234CS RGB/NIR sensor using libcamera. This 2MP global-shutter sensor delivers 1920×1200 output at up to 120 FPS, making it ideal for high-speed tracking applications.

## Key Features

- **Production libcamera integration** with automatic pixel format negotiation
- **Mock fallback mode** for development and CI environments without hardware
- **Global shutter** eliminates motion blur for fast-moving objects
- **Continuous AF/AWB** for optimal image quality
- **Efficient buffer management** with 5-buffer queue for smooth capture

## Hardware Requirements

### Supported Platforms
- **Raspberry Pi 4/5** (64-bit Bookworm)
- **NVIDIA Jetson Orin Nano** (JetPack 5.1+)
- **Any libcamera-compatible board** with CSI interface

### Camera Module
- OnSemi AR0234CS sensor
- Compatible carrier boards (e.g., Arducam B0429)
- Proper device tree overlay for your platform

## Installation

### Raspberry Pi (Recommended)

```bash
# Install libcamera and Python bindings
sudo apt update
sudo apt install -y libcamera-dev libcamera-tools \
                    python3-libcamera python3-picamera2

# Verify installation
libcamera-hello --list-cameras
```

### NVIDIA Jetson Orin Nano

```bash
# JetPack 5.1+ includes libcamera libraries
# Install Python bindings
sudo apt install -y python3-dev ninja-build meson
git clone https://git.libcamera.org/libcamera/libcamera.git
cd libcamera
meson setup build
ninja -C build
sudo ninja -C build install
```

### Device Tree Configuration

Apply the appropriate device tree overlay for your camera module:

```bash
# Example for Arducam AR0234 on Raspberry Pi
sudo dtoverlay=arducam-ar0234

# Or add to /boot/config.txt:
dtoverlay=arducam-ar0234
```

## Usage

### Basic Integration

The AR0234CS driver is automatically used by the SensorHub:

```python
from visionangel_pod.sensor_interface.sensor_hub import SensorHub

# Initialize sensor hub
hub = SensorHub()
hub.initialize_all()
hub.start_all_capture()

# Get frames
frames = hub.get_all_frames()
if frames and frames['rgb'] is not None:
    rgb_frame = frames['rgb']  # Shape: (1200, 1920, 3)
    print(f"Frame shape: {rgb_frame.shape}")

hub.stop_all_capture()
```

### Direct Sensor Access

```python
from visionangel_pod.sensor_interface.rgb_ar0234 import AR0234CS

# Create sensor with custom settings
sensor = AR0234CS(name="Primary_RGB", fps_target=30)

# Initialize and start
if sensor.initialize():
    sensor.start_capture()
    
    # Read frames
    frame = sensor.read_frame()
    if frame is not None:
        print(f"Captured frame: {frame.shape}")
    
    sensor.stop_capture()
```

## Configuration

### Supported Pixel Formats

The driver automatically negotiates the best available format:

- `SRGGB10` - 10-bit Bayer (preferred)
- `SRGGB10_CSI2P` - 10-bit Bayer packed
- `SBGGR10` - 10-bit Bayer alternative
- `YUYV` - YUV 4:2:2 (processed)

### Performance Tuning

```python
# High-speed capture (thermal management required)
sensor = AR0234CS(fps_target=120)

# Conservative settings for development
sensor = AR0234CS(fps_target=30)
```

## Troubleshooting

### Common Issues

| Symptom | Likely Cause | Solution |
|---------|--------------|----------|
| `No cameras found by libcamera` | Device tree overlay missing | Apply correct DT overlay and reboot |
| `No supported pixel format found` | Sensor driver issues | Check `dmesg` for CSI errors |
| `Cannot allocate memory` | Insufficient CMA pool | Increase `cma=256M` in boot config |
| `YUYV colors appear wrong` | Color space mismatch | Verify OpenCV color conversion |

### Diagnostic Commands

```bash
# List available cameras
libcamera-hello --list-cameras

# Test basic capture
libcamera-still -o test.jpg

# Check kernel messages
dmesg | grep -i ar0234

# Verify device tree
dtc -I fs /proc/device-tree | grep -i camera
```

### Mock Mode

When libcamera is not available, the driver automatically falls back to mock mode:

```
WARNING - libcamera not found. AR0234CS driver will run in MOCK mode.
INFO - RGB_Primary (Mock) initialized.
```

Mock mode generates random frames at the correct resolution for testing.

## Integration with VisionAngel

The AR0234CS driver integrates seamlessly with the VisionAngel tracking pipeline:

1. **Frame Capture**: High-speed global shutter eliminates motion blur
2. **Object Detection**: Full 1920×1200 resolution for detailed analysis  
3. **Multi-Object Tracking**: Consistent frame timing for accurate tracking
4. **WebSocket Streaming**: Real-time frame delivery to connected clients

## Performance Characteristics

- **Resolution**: 1920×1200 (2.3MP)
- **Frame Rate**: Up to 120 FPS (hardware dependent)
- **Latency**: <16ms at 60 FPS
- **Power**: ~2W typical operation
- **Interface**: MIPI CSI-2 (4-lane)

## Next Steps

1. **Thermal Sensor**: Add Lepton 3.5 or AMG8833 integration
2. **Radar Module**: Integrate CAN/SPI radar for sensor fusion
3. **Advanced ISP**: Implement custom image processing pipeline
4. **Multi-Camera**: Support for stereo or multi-sensor arrays

## References

- [AR0234CS Datasheet](https://www.onsemi.com/products/sensors/image-sensors/ar0234cs)
- [libcamera Documentation](https://libcamera.org/docs.html)
- [Raspberry Pi Camera Documentation](https://www.raspberrypi.org/documentation/accessories/camera.html)
- [NVIDIA Jetson Camera Guide](https://developer.nvidia.com/embedded/learn/tutorials/first-picture-csi-usb-camera)
