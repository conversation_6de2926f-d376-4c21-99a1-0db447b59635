# VisionAngel Pod Hardware Setup Guide

## Quick Start for AR0234CS Camera

### Raspberry Pi 4/5 (Recommended)

1. **Install libcamera stack:**
```bash
sudo apt update
sudo apt install -y libcamera-dev libcamera-tools \
                    python3-libcamera python3-picamera2
```

2. **Enable camera interface:**
```bash
# Add to /boot/config.txt
echo "dtoverlay=arducam-ar0234" | sudo tee -a /boot/config.txt
sudo reboot
```

3. **Verify camera detection:**
```bash
libcamera-hello --list-cameras
# Should show AR0234CS sensor
```

4. **Run VisionAngel Pod:**
```bash
source .venv/bin/activate
python -m visionangel_pod.main_pod_app
```

**Expected output:**
```
INFO AR0234CS initialized with format SRGGB10 @ 60 FPS.
INFO RGB_Primary capture started.
```

### NVIDIA Jetson Orin Nano

1. **Flash latest JetPack** (includes libcamera)

2. **Install Python bindings:**
```bash
sudo apt install -y python3-dev ninja-build meson
git clone https://git.libcamera.org/libcamera/libcamera.git
cd libcamera
meson setup build
ninja -C build
sudo ninja -C build install
```

3. **Apply device tree overlay** (if using Arducam module):
```bash
# Follow vendor-specific instructions
# Example for Arducam B0429:
sudo ./install_driver.sh
```

4. **Test and run** (same as Raspberry Pi steps 3-4)

## Troubleshooting

### No cameras found
```bash
# Check device tree
dtc -I fs /proc/device-tree | grep -i camera

# Check kernel messages
dmesg | grep -i ar0234

# Verify CSI connection
v4l2-ctl --list-devices
```

### Format negotiation issues
- Add `RAW16` to `SUPPORTED_FMTS` in `rgb_ar0234.py`
- Check available formats: `libcamera-hello --list-cameras`

### Memory allocation errors
```bash
# Increase CMA pool in /boot/config.txt
echo "cma=256M" | sudo tee -a /boot/config.txt
sudo reboot
```

## Development Mode

Without hardware, the driver automatically runs in mock mode:
```bash
python tools/test_ar0234cs.py
# ✅ All tests passed!
# 🚀 Ready for real photons!
```

## Next Steps

1. **Verify live capture** with WebSocket viewer
2. **Add thermal sensor** (Lepton 3.5)
3. **Integrate radar module** for sensor fusion
4. **Deploy in production** environment

## Support

- Check `docs/AR0234CS_INTEGRATION.md` for detailed documentation
- Run `python tools/test_ar0234cs.py` to verify integration
- Monitor logs for initialization and capture status
