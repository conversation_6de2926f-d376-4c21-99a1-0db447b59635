import numpy as np
from scipy.optimize import linear_sum_assignment

def iou(b1, b2):
    """Intersection-over-Union for two [x, y, w, h] boxes."""
    x1, y1, w1, h1 = b1
    x2, y2, w2, h2 = b2
    xa, ya = max(x1, x2), max(y1, y2)
    xb, yb = min(x1 + w1, x2 + w2), min(y1 + h1, y2 + h2)
    inter = max(0, xb - xa) * max(0, yb - ya)
    union = w1 * h1 + w2 * h2 - inter
    return inter / union if union else 0.0

class Track:
    _next_id = 1

    def __init__(self, bbox, conf, cls):
        self.id = Track._next_id
        Track._next_id += 1
        self.bbox = bbox
        self.confidence = conf
        self.class_id = cls
        self.misses = 0
        self.age = 1
        self.hits = 1

    def predict(self):
        pass

    def update(self, bbox, conf):
        self.bbox = bbox
        self.confidence = conf
        self.misses = 0
        self.hits += 1
        self.age += 1

class MultiObjectTracker:
    def __init__(self, max_misses=8, iou_threshold=0.3, lambda_app=0.4):
        self.tracks = []
        self.max_misses = max_misses
        self.iou_threshold = iou_threshold
        self.lambda_app = lambda_app

    def _build_cost(self, track_bboxes, det_bboxes):
        cost = np.zeros((len(track_bboxes), len(det_bboxes)), dtype=float)
        for i, tb in enumerate(track_bboxes):
            for j, db in enumerate(det_bboxes):
                score = 1.0 - iou(tb, db)
                cost[i, j] = score if score <= (1.0 - self.iou_threshold) else 1e6
        return cost

    def update(self, detections, embeds=None):
        if not self.tracks:
            self.tracks = [Track(*d) for d in detections]
            return list(self.tracks)

        track_bboxes = [t.bbox for t in self.tracks]
        det_bboxes = [d[0] for d in detections]
        cost = self._build_cost(track_bboxes, det_bboxes)

        t_idx, d_idx = linear_sum_assignment(cost)
        matched, unmatched_t, unmatched_d = [], set(range(len(self.tracks))), set(range(len(detections)))

        for ti, di in zip(t_idx, d_idx):
            if cost[ti, di] < 1e6:
                matched.append((ti, di))
                unmatched_t.discard(ti)
                unmatched_d.discard(di)

        updated_tracks = []
        for ti, di in matched:
            bbox, conf, _ = detections[di]
            self.tracks[ti].update(bbox, conf)
            updated_tracks.append(self.tracks[ti])

        for ti in unmatched_t:
            t = self.tracks[ti]
            t.misses += 1
            t.age += 1
            if t.misses <= self.max_misses:
                updated_tracks.append(t)

        updated_tracks.extend(Track(*detections[di]) for di in unmatched_d)
        self.tracks = updated_tracks
        return list(self.tracks)
