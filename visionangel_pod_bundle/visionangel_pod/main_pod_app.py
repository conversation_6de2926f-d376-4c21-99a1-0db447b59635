import asyncio
import time
from .utils.logger import app_logger
from .config_manager import config
from .sensor_interface.sensor_hub import SensorHub
from .ai_processing.reid_embedder import ReIDEmbedder
from .ai_processing.kalman_mot import MultiObjectTracker
from .communication.websocket_server import WebSocketServer
from .ai_processing.fusion_model_interface import FusionAIModel

class VisionAngelPodApp:
    def __init__(self):
        self.sensor_hub = SensorHub()
        self.detector = FusionAIModel()
        self.reid = ReIDEmbedder()
        self.tracker = MultiObjectTracker(
            max_misses=config.get('tracker_max_misses'),
            iou_threshold=config.get('tracker_iou_threshold'),
            lambda_app=config.get('tracker_lambda_app')
        )
        self.ws = WebSocketServer(port=config.get('websocket_port'))
        self.running = True
        self.fps = config.get('fps_target')

    async def run(self):
        await self.ws.start()
        self.sensor_hub.initialize_all()
        self.sensor_hub.start_all_capture()
        frame = 0
        while self.running:
            s = time.time()
            frames = self.sensor_hub.get_all_frames()
            if not frames:
                await asyncio.sleep(0.01)
                continue
            detections = [(d['bbox_xywh'], d['confidence'], d['class_id']) for d in self.detector.infer(frames)]
            embeds = [self.reid.extract_embedding(frames['rgb'], d[0]) for d in detections]
            tracks = self.tracker.update(detections, embeds)

            payload = {
                "pod_id": config.get('pod_id'),
                "frame_id": frame,
                "tracked_objects": [{"track_id": t.id, "bbox_xywh": t.bbox} for t in tracks]
            }
            await self.ws.broadcast(payload)
            frame += 1
            await asyncio.sleep(max(0, 1.0/self.fps - (time.time() - s)))

if __name__ == '__main__':
    asyncio.run(VisionAngelPodApp().run())
