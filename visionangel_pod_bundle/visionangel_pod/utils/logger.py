import logging, sys
def setup_logger(name="VisionAngelPod", level=logging.INFO):
    lg = logging.getLogger(name); lg.propagate = False; lg.setLevel(level)
    if not lg.handlers:
        h = logging.StreamHandler(sys.stdout)
        h.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            '%Y-%m-%d %H:%M:%S'))
        lg.addHandler(h)
    return lg
app_logger = setup_logger()
