import asyncio
import json
import websockets

class WebSocketServer:
    def __init__(self, port=8765):
        self.port = port
        self.clients = set()

    async def start(self):
        self.server = await websockets.serve(self.handle_client, "0.0.0.0", self.port)

    async def handle_client(self, websocket, path):
        self.clients.add(websocket)
        try:
            await websocket.wait_closed()
        finally:
            self.clients.remove(websocket)

    async def broadcast(self, data):
        if self.clients:
            message = json.dumps(data)
            await asyncio.gather(
                *[client.send(message) for client in self.clients],
                return_exceptions=True
            )
