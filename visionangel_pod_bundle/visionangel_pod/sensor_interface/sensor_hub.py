from .rgb_ar0234 import AR0234CS

class SensorHub:
    def __init__(self):
        self.rgb_sensor = AR0234CS()

    def initialize_all(self):
        return self.rgb_sensor.initialize()

    def start_all_capture(self):
        return self.rgb_sensor.start_capture()

    def get_all_frames(self):
        rgb_frame = self.rgb_sensor.get_frame()
        return {"rgb": rgb_frame} if rgb_frame is not None else None

    def stop_all_capture(self):
        return self.rgb_sensor.stop_capture()
