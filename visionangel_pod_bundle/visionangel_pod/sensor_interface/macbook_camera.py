from __future__ import annotations
import cv2
import numpy as np
import time
from .base_sensor import BaseSensor
from visionangel_pod.utils.logger import app_logger


class MacBookCamera(BaseSensor):
    """MacBook built-in camera driver for VisionAngel Pod testing."""
    
    NAME = "MacBook_Camera"
    DEFAULT_FPS = 30
    
    def __init__(self, name: str = NAME, fps_target: int = DEFAULT_FPS, device_id: int = 0):
        super().__init__(name, fps_target, device_id)
        self.cap = None
        self.device_id = device_id if isinstance(device_id, int) else 0
        
    def initialize(self) -> bool:
        """Initialize the MacBook camera using OpenCV."""
        try:
            app_logger.info(f"Initializing {self.name} on device {self.device_id}...")
            
            # Try to open the camera
            self.cap = cv2.VideoCapture(self.device_id)
            
            if not self.cap.isOpened():
                app_logger.error(f"Failed to open camera device {self.device_id}")
                return False
            
            # Set camera properties for better performance
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps_target)
            
            # Get actual camera properties
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            app_logger.info(f"{self.name} initialized successfully")
            app_logger.info(f"  Resolution: {width}x{height}")
            app_logger.info(f"  FPS: {fps}")
            
            return True
            
        except Exception as e:
            app_logger.error(f"Failed to initialize {self.name}: {e}")
            return False
    
    def start_capture(self) -> bool:
        """Start capturing frames."""
        if not self.cap or not self.cap.isOpened():
            app_logger.error("Camera not initialized")
            return False
            
        self.is_active = True
        app_logger.info(f"{self.name} capture started")
        return True
    
    def read_frame(self) -> np.ndarray | None:
        """Read a frame from the MacBook camera."""
        if not self.is_active or not self.cap or not self.cap.isOpened():
            return None
            
        self._ensure_fps()
        
        try:
            ret, frame = self.cap.read()
            if ret and frame is not None:
                # Convert BGR to RGB for consistency with AR0234CS
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                return frame_rgb
            else:
                app_logger.warning("Failed to read frame from camera")
                return None
                
        except Exception as e:
            app_logger.error(f"Error reading frame: {e}")
            return None
    
    def stop_capture(self) -> bool:
        """Stop capturing frames."""
        self.is_active = False
        
        try:
            if self.cap:
                self.cap.release()
                app_logger.info(f"{self.name} capture stopped")
            return True
            
        except Exception as e:
            app_logger.error(f"Error stopping {self.name}: {e}")
            return False
    
    def get_frame_info(self) -> dict:
        """Get current frame information."""
        if not self.cap:
            return {}
            
        return {
            "width": int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            "height": int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            "fps": self.cap.get(cv2.CAP_PROP_FPS),
            "format": "RGB",
            "device_id": self.device_id
        }
