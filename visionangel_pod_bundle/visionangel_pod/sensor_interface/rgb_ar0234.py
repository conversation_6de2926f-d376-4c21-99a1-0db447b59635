from __future__ import annotations
import numpy as np
import os
from .base_sensor import BaseSensor
from visionangel_pod.utils.logger import app_logger

# Use try/except for libcamera to allow for mock fallback
# fmt: off
try:
    from libcamera import CameraManager, StreamRole, controls  # type: ignore
    LIBCAMERA_AVAILABLE = True
except ImportError:
    LIBCAMERA_AVAILABLE = False
# fmt: on

# Supported pixel formats from the AR0234CS sensor
SUPPORTED_FMTS: set[str] = {"SRGGB10", "SRGGB10_CSI2P", "SBGGR10", "YUYV"}


class AR0234CS(BaseSensor):
    """Production driver for the OnSemi AR0234CS RGB/NIR sensor using libcamera.
    Falls back to a mock implementation if libcamera is not available.
    """

    NAME = "RGB_Primary"
    NATIVE_SIZE = (1920, 1200)  # full-resolution W, H
    DEFAULT_FPS = 60  # Throttled for development to manage thermals

    def __init__(self, name: str = NAME, fps_target: int = DEFAULT_FPS, device_id: str | None = None):
        super().__init__(name, fps_target, device_id)
        self.is_mock = not LIBCAMERA_AVAILABLE
        if self.is_mock:
            app_logger.warning("libcamera not found. AR0234CS driver will run in MOCK mode.")

    # ---------------------------------------------------------------------
    # Initialisation + Helper
    # ---------------------------------------------------------------------
    def initialize(self) -> bool:
        """Initialises the camera using libcamera or sets up for mock mode."""
        if self.is_mock:
            app_logger.info(f"{self.name} (Mock) initialized.")
            return True

        try:
            self.cm = CameraManager.singleton()
            self.cm.start()
            cam_id = self._find_id(self.cm)
            if not cam_id:
                return False

            self._cam = self.cm.get(cam_id)
            # Viewfinder for processed formats, Raw for Bayer
            cfg = self._cam.generate_configuration([StreamRole.Viewfinder])

            formats = self._cam.camera_properties["PixelFormats"]
            sup_fmt_name = next((f.name for f in formats if f.name in SUPPORTED_FMTS), None)
            if not sup_fmt_name:
                app_logger.error(
                    f"AR0234CS: No supported pixel format found. Available: {[f.name for f in formats]}"
                )
                return False

            cfg[0].size = self.NATIVE_SIZE
            cfg[0].pixel_format = sup_fmt_name
            cfg[0].framerate = self.fps_target

            # Optional: enable continuous AF and AWB
            self._cam.controls.set(controls.AfModeEnum.Continuous, True)
            self._cam.controls.set(controls.AwbModeEnum.Auto, True)

            status = self._cam.configure(cfg)
            if status != 0:
                app_logger.error(f"Failed to configure camera. Status: {status}")
                return False

            self._allocator = self._cam.allocator
            self.stream = self._cam.streams[0]
            self._buffers = [self._allocator.allocate(self.stream) for _ in range(5)]
            self._requests = [self._cam.create_request() for _ in range(5)]

            app_logger.info(f"AR0234CS initialized with format {sup_fmt_name} @ {self.fps_target} FPS.")
            return True
        except Exception as e:
            app_logger.error(f"Failed to initialize libcamera for AR0234CS: {e}", exc_info=True)
            return False

    # ------------------------------------------------------------------
    # Capture loop helpers
    # ------------------------------------------------------------------
    def read_frame(self) -> np.ndarray | None:
        """Grabs a frame from the camera queue or returns a mock frame."""
        if not self.is_active:
            return None

        self._ensure_fps()

        if self.is_mock:
            return np.random.randint(0, 256, (self.NATIVE_SIZE[1], self.NATIVE_SIZE[0], 3), dtype=np.uint8)

        try:
            request = self._cam.capture_request()
            if request:
                result = request.result()
                if result.status != request.Status.Complete:
                    app_logger.warning(f"Frame capture failed with status: {result.status}")
                    self._cam.release_request(request)
                    return None

                buffer = request.buffers[self.stream]
                frame_data = buffer.mmap()
                cfg = self.stream.configuration
                width, height, stride = cfg.size.width, cfg.size.height, cfg.stride

                frame = np.frombuffer(frame_data, dtype=np.uint8).reshape(height, stride, -1)
                frame = frame[:, :width, :]
                if cfg.pixel_format == "YUYV":
                    import cv2  # local import to avoid hard dependency when not needed

                    frame = cv2.cvtColor(frame, cv2.COLOR_YUV2BGR_YUYV)

                self._cam.release_request(request)
                return frame
            return None
        except Exception as e:
            app_logger.error(f"Error reading frame from libcamera: {e}")
            return None

    def start_capture(self) -> bool:
        """Starts the camera stream."""
        if self.is_mock:
            self.is_active = True
            return True
        try:
            self._cam.start()
            for request in self._requests:
                for buf in self._buffers:
                    request.add_buffer(self.stream, buf)
                self._cam.queue_request(request)
            self.is_active = True
            app_logger.info(f"{self.name} capture started.")
            return True
        except Exception as e:
            app_logger.error(f"Failed to start AR0234CS capture: {e}")
            return False

    def stop_capture(self) -> bool:
        """Stops the camera stream."""
        if self.is_mock:
            self.is_active = False
            return True
        try:
            if hasattr(self, "_cam") and self._cam:
                self._cam.stop()
            app_logger.info(f"{self.name} capture stopped.")
        except Exception as e:
            app_logger.error(f"Error stopping AR0234CS capture: {e}")
        self.is_active = False
        return True

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------
    def _find_id(self, cm: CameraManager) -> str | None:
        """Finds the camera ID matching 'ar0234'."""
        if not cm.cameras:
            app_logger.error("No cameras found by libcamera.")
            return None
        for cam in cm.cameras:
            if "ar0234" in cam.id.lower():
                return cam.id
        app_logger.warning(
            f"AR0234CS camera not found. Available: {[c.id for c in cm.cameras]}. Using first camera as fallback."
        )
        return cm.cameras[0].id
