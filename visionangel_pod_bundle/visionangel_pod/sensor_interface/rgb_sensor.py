import cv2
import numpy as np
from .base_sensor import BaseSensor

class RGBSensor(BaseSensor):
    def __init__(self, device_id=0):
        self.device_id = device_id
        self.cap = None

    def initialize(self):
        self.cap = cv2.VideoCapture(self.device_id)
        return self.cap.isOpened()

    def start_capture(self):
        pass

    def get_frame(self):
        if self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            return frame if ret else None
        return None

    def stop_capture(self):
        if self.cap:
            self.cap.release()
