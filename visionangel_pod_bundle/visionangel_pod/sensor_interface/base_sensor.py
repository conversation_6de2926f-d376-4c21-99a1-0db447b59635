from __future__ import annotations
import abc
import time
import numpy as np


class BaseSensor(abc.ABC):
    """Base class for all sensor implementations."""

    def __init__(self, name: str, fps_target: int = 30, device_id: str | None = None):
        self.name = name
        self.fps_target = fps_target
        self.device_id = device_id
        self.is_active = False
        self._last_frame_time = 0.0

    @abc.abstractmethod
    def initialize(self) -> bool:
        """Initialize the sensor. Returns True if successful."""
        pass

    @abc.abstractmethod
    def start_capture(self) -> bool:
        """Start capturing frames. Returns True if successful."""
        pass

    @abc.abstractmethod
    def stop_capture(self) -> bool:
        """Stop capturing frames. Returns True if successful."""
        pass

    def get_frame(self) -> np.ndarray | None:
        """Get a frame from the sensor. Default implementation calls read_frame."""
        return self.read_frame()

    def read_frame(self) -> np.ndarray | None:
        """Read a frame from the sensor. Should be implemented by subclasses."""
        return None

    def _ensure_fps(self) -> None:
        """Ensure we don't exceed the target FPS."""
        if self.fps_target <= 0:
            return

        current_time = time.time()
        time_since_last = current_time - self._last_frame_time
        min_frame_time = 1.0 / self.fps_target

        if time_since_last < min_frame_time:
            time.sleep(min_frame_time - time_since_last)

        self._last_frame_time = time.time()
