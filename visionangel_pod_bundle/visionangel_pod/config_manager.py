import json, os
from visionangel_pod.utils.logger import app_logger
DEFAULT_CONFIG = {
    "pod_id": "VA_POD_001",
    "log_level": "INFO",
    "fps_target": 10,
    "calibration_file_path": "calibration/calibration_pod_A001.json",
    "websocket_host": "0.0.0.0",
    "websocket_port": 8765,
    "http_port": 8080,
    "enable_zoom_sr": False,
    "fusion_model_path": "models/fusion/visionangel_fusion_v1.pt",
    "reid_model_path": "models/reid/osnet_x0_25_msmt17.pt",
    "tracker_max_misses": 8,
    "tracker_iou_threshold": 0.25,
    "tracker_lambda_app": 0.4,
    "use_gemma": False
}
class ConfigManager:
    def __init__(self, path=None):
        self.config = DEFAULT_CONFIG.copy()
        if path and os.path.exists(path):
            try:
                self.config.update(json.load(open(path)))
                app_logger.info(f"Loaded config {path}")
            except Exception as e:
                app_logger.error(f"Config error {e}")
        for k,v in DEFAULT_CONFIG.items():
            if (env:=os.getenv(f"VA_{k.upper()}")) is not None:
                self.config[k] = type(v)(env)
    def get(self,k,d=None): return self.config.get(k,d)
    def all(self): return self.config.copy()
config = ConfigManager(os.getenv("VA_CONFIG_PATH"))
