#!/usr/bin/env python3
"""
VisionAngel Pod MacBook Camera Demo

This script demonstrates the VisionAngel Pod system using your MacBook's
built-in camera for real-time computer vision processing.
"""

import asyncio
import json
import time
import sys
import os
import cv2

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def run_macbook_demo():
    """Run VisionAngel Pod demo with MacBook camera."""
    
    print("🎥 VISIONANGEL POD - MACBOOK CAMERA DEMO 🎥\n")
    
    try:
        # Import components
        from visionangel_pod.sensor_interface.macbook_camera import MacBookCamera
        from visionangel_pod.ai_processing.fusion_model_interface import FusionAIModel
        from visionangel_pod.ai_processing.kalman_mot import MultiObjectTracker
        from visionangel_pod.ai_processing.reid_embedder import ReIDEmbedder
        from visionangel_pod.config_manager import config
        
        print("✅ All components imported successfully")
        
        # Initialize components
        print("\n📡 Initializing VisionAngel Pod with MacBook camera...")
        
        camera = MacBookCamera(fps_target=15)  # Lower FPS for stability
        detector = FusionAIModel()
        tracker = MultiObjectTracker(
            max_misses=config.get('tracker_max_misses'),
            iou_threshold=config.get('tracker_iou_threshold'),
            lambda_app=config.get('tracker_lambda_app')
        )
        reid = ReIDEmbedder()
        
        print(f"✅ Camera: {camera.name}")
        print(f"✅ AI Detector: {type(detector).__name__}")
        print(f"✅ Tracker: {type(tracker).__name__}")
        print(f"✅ ReID Embedder: {type(reid).__name__}")
        
        # Initialize camera
        print("\n🎥 Starting MacBook camera...")
        if not camera.initialize():
            print("❌ Failed to initialize camera")
            print("💡 Make sure no other apps are using the camera")
            return False
            
        if not camera.start_capture():
            print("❌ Failed to start camera capture")
            return False
            
        # Get camera info
        info = camera.get_frame_info()
        print(f"✅ Camera initialized: {info['width']}x{info['height']} @ {info['fps']} FPS")
        
        # Test frame capture
        print("\n📸 Testing frame capture...")
        test_frame = camera.read_frame()
        if test_frame is not None:
            print(f"✅ Frame captured: {test_frame.shape} {test_frame.dtype}")
        else:
            print("❌ Failed to capture test frame")
            return False
        
        # Main processing loop
        print(f"\n🔄 Starting real-time processing with LIVE CAMERA...")
        print(f"   Pod ID: {config.get('pod_id')}")
        print(f"   Target FPS: 15 (optimized for MacBook)")
        print(f"   Press Ctrl+C to stop")
        print("\n" + "="*70)
        
        frame_count = 0
        start_time = time.time()
        
        try:
            while True:  # Run continuously until stopped
                loop_start = time.time()
                
                # Get real frame from MacBook camera
                frame = camera.read_frame()
                if frame is None:
                    print("⚠️  No frame from camera")
                    await asyncio.sleep(0.1)
                    continue
                
                # Create frames dict for AI processing
                frames = {"rgb": frame}
                
                # Run AI detection on real camera frame
                detections = detector.infer(frames)
                
                # Convert to tracker format
                det_tuples = [(d['bbox_xywh'], d['confidence'], d['class_id']) for d in detections]
                
                # Generate ReID embeddings for real detections
                embeds = []
                if det_tuples:
                    embeds = [reid.extract_embedding(frame, d[0]) for d in det_tuples]
                
                # Update tracker with real detections
                tracks = tracker.update(det_tuples, embeds)
                
                # Create output payload
                payload = {
                    "pod_id": config.get('pod_id'),
                    "frame_id": frame_count,
                    "timestamp": time.time(),
                    "camera": "MacBook",
                    "resolution": f"{frame.shape[1]}x{frame.shape[0]}",
                    "fps": round(1.0 / (time.time() - loop_start + 0.001), 1),
                    "detections": len(detections),
                    "tracked_objects": [
                        {
                            "track_id": t.id,
                            "bbox_xywh": t.bbox,
                            "confidence": t.confidence,
                            "age": t.age,
                            "hits": t.hits
                        } for t in tracks
                    ]
                }
                
                # Display real-time status
                elapsed = time.time() - start_time
                avg_fps = frame_count / elapsed if elapsed > 0 else 0
                
                print(f"📹 Frame {frame_count:3d} | "
                      f"Real Camera: {frame.shape[1]}x{frame.shape[0]} | "
                      f"Detections: {len(detections)} | "
                      f"Tracks: {len(tracks)} | "
                      f"FPS: {payload['fps']:4.1f} | "
                      f"Avg: {avg_fps:4.1f}")
                
                # Show track details for real objects
                if tracks:
                    for track in tracks[:3]:  # Show first 3 tracks
                        print(f"  🎯 Track {track.id}: bbox={track.bbox}, age={track.age}, hits={track.hits}")
                
                frame_count += 1
                
                # Maintain target FPS (15 FPS for stability)
                target_frame_time = 1.0 / 15
                elapsed_frame_time = time.time() - loop_start
                sleep_time = max(0, target_frame_time - elapsed_frame_time)
                await asyncio.sleep(sleep_time)
                
        except KeyboardInterrupt:
            print("\n⏹️  Demo stopped by user")
        
        # Cleanup
        print("\n🛑 Stopping MacBook camera...")
        camera.stop_capture()
        print("✅ Camera stopped")
        
        # Final statistics
        total_time = time.time() - start_time
        final_fps = frame_count / total_time if total_time > 0 else 0
        
        print("\n" + "="*70)
        print("📊 MACBOOK CAMERA DEMO STATISTICS")
        print("="*70)
        print(f"Total frames processed: {frame_count}")
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Average FPS: {final_fps:.2f}")
        print(f"Camera resolution: {info['width']}x{info['height']}")
        print(f"Peak tracks: {max([len(tracker.tracks)] + [0])}")
        print("✅ MacBook camera demo completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main entry point."""
    print("VisionAngel Pod MacBook Camera Demo")
    print("=" * 50)
    print("This demo uses your MacBook's built-in camera for")
    print("real-time computer vision processing!\n")
    
    print("🔒 Camera Permission Required:")
    print("   Make sure to allow camera access when prompted")
    print("   Close other apps using the camera (Zoom, etc.)\n")
    
    try:
        asyncio.run(run_macbook_demo())
    except KeyboardInterrupt:
        print("\n👋 Demo terminated by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")

if __name__ == "__main__":
    main()
