#!/usr/bin/env python3
"""
AR0234CS Driver Integration Test

This script verifies that the AR0234CS driver is properly integrated
and working in mock mode for development/CI environments.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_ar0234cs_driver():
    """Test the AR0234CS driver functionality."""
    print("=== AR0234CS Driver Integration Test ===\n")
    
    try:
        from visionangel_pod.sensor_interface.rgb_ar0234 import AR0234CS
        print("✅ AR0234CS driver imported successfully")
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False
    
    try:
        # Create sensor instance
        sensor = AR0234CS()
        print(f"✅ AR0234CS instance created")
        print(f"   Mock mode: {sensor.is_mock}")
        print(f"   Name: {sensor.name}")
        print(f"   FPS target: {sensor.fps_target}")
        print(f"   Native size: {sensor.NATIVE_SIZE}")
        
        # Test initialization
        init_result = sensor.initialize()
        print(f"✅ Initialization: {init_result}")
        
        if init_result:
            # Test capture lifecycle
            start_result = sensor.start_capture()
            print(f"✅ Start capture: {start_result}")
            
            if start_result:
                # Test frame capture
                frame = sensor.read_frame()
                if frame is not None:
                    print(f"✅ Frame captured successfully")
                    print(f"   Shape: {frame.shape}")
                    print(f"   Data type: {frame.dtype}")
                    print(f"   Resolution: {frame.shape[1]}x{frame.shape[0]} (WxH)")
                    
                    # Verify frame properties
                    expected_shape = (1200, 1920, 3)  # (H, W, C)
                    if frame.shape == expected_shape:
                        print("✅ Frame dimensions correct")
                    else:
                        print(f"⚠️  Frame dimensions: got {frame.shape}, expected {expected_shape}")
                else:
                    print("❌ No frame captured")
                    return False
                
                # Test stop capture
                stop_result = sensor.stop_capture()
                print(f"✅ Stop capture: {stop_result}")
            else:
                print("❌ Failed to start capture")
                return False
        else:
            print("❌ Failed to initialize")
            return False
            
    except Exception as e:
        print(f"❌ Driver test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_sensor_hub_integration():
    """Test SensorHub integration with AR0234CS."""
    print("\n=== SensorHub Integration Test ===\n")
    
    try:
        from visionangel_pod.sensor_interface.sensor_hub import SensorHub
        print("✅ SensorHub imported successfully")
        
        # Create hub instance
        hub = SensorHub()
        print("✅ SensorHub created")
        
        # Check sensor type
        sensor_type = type(hub.rgb_sensor).__name__
        print(f"✅ RGB sensor type: {sensor_type}")
        
        if sensor_type == 'AR0234CS':
            print("✅ SensorHub correctly using AR0234CS driver")
        else:
            print(f"❌ SensorHub using wrong sensor: {sensor_type}")
            return False
        
        # Test hub lifecycle
        hub_init = hub.initialize_all()
        print(f"✅ Hub initialization: {hub_init}")
        
        if hub_init:
            hub_start = hub.start_all_capture()
            print(f"✅ Hub start capture: {hub_start}")
            
            if hub_start:
                frames = hub.get_all_frames()
                if frames and 'rgb' in frames:
                    rgb_frame = frames['rgb']
                    print(f"✅ Hub frame capture: {rgb_frame.shape}")
                else:
                    print("❌ No frames from hub")
                    return False
                
                hub_stop = hub.stop_all_capture()
                print(f"✅ Hub stop capture: {hub_stop}")
            else:
                print("❌ Failed to start hub capture")
                return False
        else:
            print("❌ Failed to initialize hub")
            return False
            
    except Exception as e:
        print(f"❌ SensorHub test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """Run all integration tests."""
    print("AR0234CS Integration Verification\n")
    print("This test verifies the AR0234CS driver integration")
    print("in mock mode (no hardware required).\n")
    
    # Run tests
    driver_ok = test_ar0234cs_driver()
    hub_ok = test_sensor_hub_integration()
    
    # Summary
    print("\n=== Test Summary ===")
    if driver_ok and hub_ok:
        print("✅ All tests passed!")
        print("✅ AR0234CS driver successfully integrated")
        print("✅ Mock mode working for CI/development")
        print("✅ Ready for hardware deployment")
        print("\n🚀 Ready for real photons!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
