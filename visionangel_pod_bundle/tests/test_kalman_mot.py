import pytest
import numpy as np
from visionangel_pod.ai_processing.kalman_mot import MultiObjectTracker, Track, iou

def test_iou():
    bbox1 = [0, 0, 10, 10]
    bbox2 = [5, 5, 10, 10]
    result = iou(bbox1, bbox2)
    assert 0 < result < 1

def test_tracker_initialization():
    tracker = MultiObjectTracker()
    assert tracker.tracks == []
    assert tracker.max_misses == 8
    assert tracker.iou_threshold == 0.3

def test_tracker_update():
    tracker = MultiObjectTracker()
    detections = [([10, 10, 20, 30], 0.9, 1)]
    tracks = tracker.update(detections)
    assert len(tracks) == 1
    assert tracks[0].id == 1
