#!/usr/bin/env python3
"""
PlayDC Security Camera Configuration Tool

This tool helps you configure and test your PlayDC security camera
connection for the VisionAngel Pod system.
"""

import sys
import os
import cv2

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_rtsp_connection(rtsp_url, timeout=10):
    """Test RTSP connection and return camera info."""
    print(f"🔗 Testing: {rtsp_url}")
    
    try:
        cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        
        if not cap.isOpened():
            print("  ❌ Failed to open stream")
            return False, None
        
        # Try to read a frame
        ret, frame = cap.read()
        if not ret or frame is None:
            print("  ❌ Failed to read frame")
            cap.release()
            return False, None
        
        # Get camera properties
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        info = {
            'width': width,
            'height': height,
            'fps': fps,
            'frame_shape': frame.shape
        }
        
        cap.release()
        print(f"  ✅ SUCCESS! Resolution: {width}x{height}, FPS: {fps}")
        return True, info
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False, None

def main():
    """Main configuration interface."""
    print("🏢 PlayDC Security Camera Configuration Tool")
    print("=" * 60)
    print("This tool helps you configure your PlayDC security camera")
    print("for the VisionAngel Pod surveillance system.\n")
    
    # Common PlayDC camera configurations
    common_configs = [
        {
            'name': 'PlayDC Standard (Local Network)',
            'url': 'rtsp://admin:<EMAIL>:554/stream1'
        },
        {
            'name': 'PlayDC IP Camera (192.168.1.x)',
            'url': 'rtsp://admin:admin@*************/stream1'
        },
        {
            'name': 'PlayDC Security System (10.0.0.x)',
            'url': 'rtsp://security:playdc2024@**********/stream'
        },
        {
            'name': 'PlayDC Main Entrance',
            'url': 'rtsp://playdc:<EMAIL>:554/live'
        },
        {
            'name': 'Generic RTSP (Port 8554)',
            'url': 'rtsp://admin:password@*************:8554/stream'
        }
    ]
    
    print("🔍 Testing common PlayDC camera configurations...\n")
    
    working_cameras = []
    
    for i, config in enumerate(common_configs, 1):
        print(f"{i}. {config['name']}")
        success, info = test_rtsp_connection(config['url'])
        
        if success:
            working_cameras.append({
                'config': config,
                'info': info
            })
        
        print()
    
    if working_cameras:
        print("🎉 FOUND WORKING CAMERAS!")
        print("=" * 40)
        
        for i, camera in enumerate(working_cameras, 1):
            config = camera['config']
            info = camera['info']
            
            print(f"{i}. {config['name']}")
            print(f"   URL: {config['url']}")
            print(f"   Resolution: {info['width']}x{info['height']}")
            print(f"   FPS: {info['fps']}")
            print()
        
        print("📝 To use these cameras with VisionAngel Pod:")
        print("1. Copy the working RTSP URL")
        print("2. Update the PlayDC demo script with your URL")
        print("3. Or enter it in the Streamlit dashboard")
        
    else:
        print("❌ No working cameras found with common configurations.")
        print("\n🔧 MANUAL CONFIGURATION:")
        print("Please provide your PlayDC camera details:")
        
        # Manual configuration
        try:
            ip = input("Camera IP address: ").strip()
            port = input("RTSP port (default 554): ").strip() or "554"
            username = input("Username (default admin): ").strip() or "admin"
            password = input("Password: ").strip()
            stream_path = input("Stream path (default /stream1): ").strip() or "/stream1"
            
            manual_url = f"rtsp://{username}:{password}@{ip}:{port}{stream_path}"
            
            print(f"\n🔗 Testing manual configuration...")
            success, info = test_rtsp_connection(manual_url)
            
            if success:
                print(f"\n🎉 SUCCESS! Your camera is working:")
                print(f"RTSP URL: {manual_url}")
                print(f"Resolution: {info['width']}x{info['height']}")
                print(f"FPS: {info['fps']}")
                
                # Save configuration
                config_file = "playdc_camera_config.txt"
                with open(config_file, 'w') as f:
                    f.write(f"# PlayDC Camera Configuration\n")
                    f.write(f"RTSP_URL={manual_url}\n")
                    f.write(f"CAMERA_NAME=PlayDC Security Camera\n")
                    f.write(f"RESOLUTION={info['width']}x{info['height']}\n")
                    f.write(f"FPS={info['fps']}\n")
                
                print(f"\n💾 Configuration saved to: {config_file}")
                
            else:
                print("\n❌ Manual configuration failed.")
                print("Please check your camera settings and network connection.")
                
        except KeyboardInterrupt:
            print("\n👋 Configuration cancelled by user.")
        except Exception as e:
            print(f"\n❌ Configuration error: {e}")
    
    print("\n🚀 NEXT STEPS:")
    print("1. Run the PlayDC security demo:")
    print("   python tools/playdc_security_demo.py")
    print("2. Open the Video-in-Video dashboard:")
    print("   streamlit run legendary_dashboard.py")
    print("3. Connect and enjoy your surveillance system!")

if __name__ == "__main__":
    main()
