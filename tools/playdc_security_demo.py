#!/usr/bin/env python3
"""
VisionAngel Pod PlayDC Security Camera Demo with Video-in-Video

This script demonstrates the VisionAngel Pod system using PlayDC security cameras
for real-time surveillance with video-in-video capabilities.
"""

import asyncio
import json
import time
import sys
import os
import cv2
import base64
import websockets
from websockets.server import serve

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Global state for WebSocket clients
connected_clients = set()

async def register_client(websocket):
    """Register a new WebSocket client."""
    connected_clients.add(websocket)
    print(f"📱 Client connected. Total clients: {len(connected_clients)}")

async def unregister_client(websocket):
    """Unregister a WebSocket client."""
    connected_clients.discard(websocket)
    print(f"📱 Client disconnected. Total clients: {len(connected_clients)}")

async def broadcast_frame(data):
    """Broadcast frame data to all connected clients."""
    if connected_clients:
        message = json.dumps(data)
        # Send to all clients concurrently
        await asyncio.gather(
            *[client.send(message) for client in connected_clients.copy()],
            return_exceptions=True
        )

async def websocket_handler(websocket, path):
    """Handle WebSocket connections."""
    await register_client(websocket)
    try:
        await websocket.wait_closed()
    finally:
        await unregister_client(websocket)

async def run_playdc_security_demo():
    """Run VisionAngel Pod demo with PlayDC security cameras."""
    
    print("🏢 VISIONANGEL POD - PLAYDC SECURITY CAMERA DEMO 🏢\n")
    
    try:
        # Import components
        from visionangel_pod.sensor_interface.rtsp_camera import RTSPCamera
        from visionangel_pod.sensor_interface.macbook_camera import MacBookCamera
        from visionangel_pod.ai_processing.fusion_model_interface import FusionAIModel
        from visionangel_pod.ai_processing.kalman_mot import MultiObjectTracker
        from visionangel_pod.ai_processing.reid_embedder import ReIDEmbedder
        from visionangel_pod.config_manager import config
        
        print("✅ All components imported successfully")
        
        # Initialize components
        print("\n📡 Initializing VisionAngel Pod with PlayDC security cameras...")
        
        # Try to connect to PlayDC security camera first
        print("\n🔍 Attempting to connect to PlayDC security cameras...")
        security_camera = None
        
        # Common PlayDC RTSP URLs to try
        playdc_urls = [
            "rtsp://admin:<EMAIL>:554/stream1",
            "rtsp://admin:admin@*************/stream1",
            "rtsp://security:playdc2024@**********/stream",
            "rtsp://playdc:<EMAIL>:554/live",
            # Add your specific PlayDC camera URL here
            "rtsp://your-camera-ip:554/stream1"
        ]
        
        for rtsp_url in playdc_urls:
            print(f"  🔗 Trying: {rtsp_url}")
            try:
                security_camera = RTSPCamera(
                    name="PlayDC_Security_Camera",
                    fps_target=15,
                    rtsp_url=rtsp_url
                )
                
                if security_camera.initialize():
                    print(f"✅ Connected to PlayDC security camera: {rtsp_url}")
                    break
                else:
                    security_camera = None
            except Exception as e:
                print(f"  ❌ Failed: {e}")
                security_camera = None
        
        # Fallback to MacBook camera if no security camera found
        if not security_camera:
            print("\n⚠️  No PlayDC security camera found, using MacBook camera as backup")
            security_camera = MacBookCamera(fps_target=15)
            if not security_camera.initialize():
                print("❌ Failed to initialize backup camera")
                return False
        
        # Initialize AI components
        detector = FusionAIModel()
        tracker = MultiObjectTracker(
            max_misses=config.get('tracker_max_misses'),
            iou_threshold=config.get('tracker_iou_threshold'),
            lambda_app=config.get('tracker_lambda_app')
        )
        reid = ReIDEmbedder()
        
        print(f"✅ Camera: {security_camera.name}")
        print(f"✅ AI Detector: {type(detector).__name__}")
        print(f"✅ Tracker: {type(tracker).__name__}")
        print(f"✅ ReID Embedder: {type(reid).__name__}")
        
        # Start camera capture
        print(f"\n🎥 Starting {security_camera.name}...")
        if not security_camera.start_capture():
            print("❌ Failed to start camera capture")
            return False
            
        # Get camera info
        info = security_camera.get_frame_info()
        print(f"✅ Camera active: {info.get('width', 'Unknown')}x{info.get('height', 'Unknown')} @ {info.get('fps', 'Unknown')} FPS")
        
        # Test frame capture
        print("\n📸 Testing frame capture...")
        test_frame = security_camera.read_frame()
        if test_frame is not None:
            print(f"✅ Frame captured: {test_frame.shape} {test_frame.dtype}")
        else:
            print("❌ Failed to capture test frame")
            return False
        
        # Main processing loop
        print(f"\n🔄 Starting PlayDC security surveillance system...")
        print(f"   Pod ID: {config.get('pod_id')}")
        print(f"   Camera: {security_camera.name}")
        print(f"   WebSocket Port: 8765")
        print(f"   Dashboard: http://localhost:8501")
        print(f"   Press Ctrl+C to stop")
        print("\n" + "="*80)
        
        frame_count = 0
        start_time = time.time()
        
        try:
            while True:  # Run continuously until stopped
                loop_start = time.time()
                
                # Get real frame from security camera
                frame = security_camera.read_frame()
                if frame is None:
                    print("⚠️  No frame from security camera")
                    await asyncio.sleep(0.1)
                    continue
                
                # Create frames dict for AI processing
                frames = {"rgb": frame}
                
                # Run AI detection on security camera frame
                detections = detector.infer(frames)
                
                # Convert to tracker format
                det_tuples = [(d['bbox_xywh'], d['confidence'], d['class_id']) for d in detections]
                
                # Generate ReID embeddings for real detections
                embeds = []
                if det_tuples:
                    embeds = [reid.extract_embedding(frame, d[0]) for d in det_tuples]
                
                # Update tracker with real detections
                tracks = tracker.update(det_tuples, embeds)
                
                # Encode frame as JPEG for WebSocket
                success, buffer = cv2.imencode('.jpg', cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))
                if success:
                    frame_b64 = base64.b64encode(buffer).decode('utf-8')
                else:
                    frame_b64 = None
                
                # Calculate metrics
                processing_time = (time.time() - loop_start) * 1000
                current_fps = 1.0 / (time.time() - loop_start + 0.001)
                
                # Create WebSocket payload with security camera data
                payload = {
                    "pod_id": config.get('pod_id'),
                    "frame_id": frame_count,
                    "timestamp": time.time(),
                    "timestamp_unix": time.time(),
                    "camera_type": "PlayDC_Security",
                    "camera_name": security_camera.name,
                    "resolution": f"{frame.shape[1]}x{frame.shape[0]}",
                    "frame_jpeg_b64": frame_b64,
                    "gps_lat": 38.9072,  # PlayDC coordinates (example)
                    "gps_lon": -77.0369,
                    "location": "PlayDC Main Entrance",
                    "metrics": {
                        "processing_time_ms": processing_time,
                        "loop_fps": current_fps,
                        "connection_attempts": info.get('connection_attempts', 0)
                    },
                    "tracked_objects": [
                        {
                            "track_id": t.id,
                            "bbox_xywh": t.bbox,
                            "confidence": t.confidence,
                            "class_name": "person",  # Default class
                            "age": t.age,
                            "hits": t.hits
                        } for t in tracks
                    ]
                }
                
                # Broadcast to WebSocket clients
                await broadcast_frame(payload)
                
                # Display real-time status
                elapsed = time.time() - start_time
                avg_fps = frame_count / elapsed if elapsed > 0 else 0
                
                print(f"🏢 Frame {frame_count:3d} | "
                      f"Security Cam: {frame.shape[1]}x{frame.shape[0]} | "
                      f"Detections: {len(detections)} | "
                      f"Tracks: {len(tracks)} | "
                      f"FPS: {current_fps:4.1f} | "
                      f"Avg: {avg_fps:4.1f} | "
                      f"Clients: {len(connected_clients)}")
                
                # Show track details for security detections
                if tracks:
                    for track in tracks[:3]:  # Show first 3 tracks
                        print(f"  🎯 Security Track {track.id}: bbox={track.bbox}, age={track.age}, hits={track.hits}")
                
                frame_count += 1
                
                # Maintain target FPS (15 FPS for stability)
                target_frame_time = 1.0 / 15
                elapsed_frame_time = time.time() - loop_start
                sleep_time = max(0, target_frame_time - elapsed_frame_time)
                await asyncio.sleep(sleep_time)
                
        except KeyboardInterrupt:
            print("\n⏹️  Security surveillance stopped by user")
        
        # Cleanup
        print("\n🛑 Stopping security camera...")
        security_camera.stop_capture()
        print("✅ Security camera stopped")
        
        # Final statistics
        total_time = time.time() - start_time
        final_fps = frame_count / total_time if total_time > 0 else 0
        
        print("\n" + "="*80)
        print("📊 PLAYDC SECURITY SURVEILLANCE STATISTICS")
        print("="*80)
        print(f"Total frames processed: {frame_count}")
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Average FPS: {final_fps:.2f}")
        print(f"Camera: {security_camera.name}")
        print(f"Resolution: {info.get('width', 'Unknown')}x{info.get('height', 'Unknown')}")
        print(f"Peak tracks: {max([len(tracker.tracks)] + [0])}")
        print(f"WebSocket clients served: {len(connected_clients)}")
        print("✅ PlayDC security surveillance completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Security demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main entry point with WebSocket server."""
    print("VisionAngel Pod PlayDC Security Camera Demo")
    print("=" * 70)
    print("This demo connects to PlayDC security cameras for")
    print("real-time surveillance with video-in-video capabilities!\n")
    
    print("🏢 PlayDC Security Integration:")
    print("   Supports RTSP security cameras")
    print("   Real-time AI surveillance")
    print("   Geographic video anchoring")
    print("   Multi-camera dashboard support\n")
    
    print("🌐 Starting WebSocket server on port 8765...")
    
    # Start WebSocket server
    server = await serve(websocket_handler, "localhost", 8765)
    print("✅ WebSocket server started")
    
    # Run the demo
    try:
        await run_playdc_security_demo()
    except KeyboardInterrupt:
        print("\n👋 Security demo terminated by user")
    except Exception as e:
        print(f"\n❌ Security demo error: {e}")
    finally:
        server.close()
        await server.wait_closed()
        print("🛑 WebSocket server stopped")

if __name__ == "__main__":
    asyncio.run(main())
