✅ 0.1  Creating project directory skeleton…
✅ 0.2  Initialising git…
✅ 0.3  Setting up venv + deps…
Requirement already satisfied: pip in ./.venv/lib/python3.11/site-packages (24.3.1)
Collecting pip
  Downloading pip-25.1.1-py3-none-any.whl.metadata (3.6 kB)
Downloading pip-25.1.1-py3-none-any.whl (1.8 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.8/1.8 MB 10.7 MB/s eta 0:00:00
Installing collected packages: pip
  Attempting uninstall: pip
    Found existing installation: pip 24.3.1
    Uninstalling pip-24.3.1:
      Successfully uninstalled pip-24.3.1
Successfully installed pip-25.1.1
Collecting numpy (from -r requirements.txt (line 2))
  Using cached numpy-2.2.6-cp311-cp311-macosx_14_0_arm64.whl.metadata (62 kB)
Collecting websockets (from -r requirements.txt (line 3))
  Using cached websockets-15.0.1-cp311-cp311-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting opencv-python (from -r requirements.txt (line 4))
  Using cached opencv_python-*********-cp37-abi3-macosx_13_0_arm64.whl.metadata (20 kB)
Collecting filterpy (from -r requirements.txt (line 5))
  Using cached filterpy-1.4.5-py3-none-any.whl
Collecting scipy (from -r requirements.txt (line 6))
  Using cached scipy-1.15.3-cp311-cp311-macosx_14_0_arm64.whl.metadata (61 kB)
Collecting torch (from -r requirements.txt (line 7))
  Downloading torch-2.7.1-cp311-none-macosx_11_0_arm64.whl.metadata (29 kB)
Collecting torchvision (from -r requirements.txt (line 8))
  Downloading torchvision-0.22.1-cp311-cp311-macosx_11_0_arm64.whl.metadata (6.1 kB)
Collecting Pillow (from -r requirements.txt (line 9))
  Using cached pillow-11.2.1-cp311-cp311-macosx_11_0_arm64.whl.metadata (8.9 kB)
Collecting fastapi (from -r requirements.txt (line 11))
  Using cached fastapi-0.115.12-py3-none-any.whl.metadata (27 kB)
Collecting pytest (from -r requirements.txt (line 14))
  Downloading pytest-8.4.0-py3-none-any.whl.metadata (7.7 kB)
Collecting pytest-cov (from -r requirements.txt (line 15))
  Downloading pytest_cov-6.1.1-py3-none-any.whl.metadata (28 kB)
Collecting pytest-asyncio (from -r requirements.txt (line 16))
  Downloading pytest_asyncio-1.0.0-py3-none-any.whl.metadata (4.0 kB)
Collecting ruff (from -r requirements.txt (line 17))
  Downloading ruff-0.11.13-py3-none-macosx_11_0_arm64.whl.metadata (25 kB)
Collecting black (from -r requirements.txt (line 18))
  Using cached black-25.1.0-cp311-cp311-macosx_11_0_arm64.whl.metadata (81 kB)
Collecting pydocstyle (from -r requirements.txt (line 19))
  Downloading pydocstyle-6.3.0-py3-none-any.whl.metadata (3.7 kB)
Collecting pre-commit (from -r requirements.txt (line 20))
  Downloading pre_commit-4.2.0-py2.py3-none-any.whl.metadata (1.3 kB)
Collecting uvicorn[standard] (from -r requirements.txt (line 12))
  Using cached uvicorn-0.34.3-py3-none-any.whl.metadata (6.5 kB)
Collecting matplotlib (from filterpy->-r requirements.txt (line 5))
  Using cached matplotlib-3.10.3-cp311-cp311-macosx_11_0_arm64.whl.metadata (11 kB)
Collecting filelock (from torch->-r requirements.txt (line 7))
  Using cached filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting typing-extensions>=4.10.0 (from torch->-r requirements.txt (line 7))
  Using cached typing_extensions-4.14.0-py3-none-any.whl.metadata (3.0 kB)
Collecting sympy>=1.13.3 (from torch->-r requirements.txt (line 7))
  Using cached sympy-1.14.0-py3-none-any.whl.metadata (12 kB)
Collecting networkx (from torch->-r requirements.txt (line 7))
  Using cached networkx-3.5-py3-none-any.whl.metadata (6.3 kB)
Collecting jinja2 (from torch->-r requirements.txt (line 7))
  Using cached jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)
Collecting fsspec (from torch->-r requirements.txt (line 7))
  Using cached fsspec-2025.5.1-py3-none-any.whl.metadata (11 kB)
Collecting starlette<0.47.0,>=0.40.0 (from fastapi->-r requirements.txt (line 11))
  Using cached starlette-0.46.2-py3-none-any.whl.metadata (6.2 kB)
Collecting pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4 (from fastapi->-r requirements.txt (line 11))
  Using cached pydantic-2.11.5-py3-none-any.whl.metadata (67 kB)
Collecting annotated-types>=0.6.0 (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi->-r requirements.txt (line 11))
  Using cached annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.33.2 (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi->-r requirements.txt (line 11))
  Using cached pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting typing-inspection>=0.4.0 (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi->-r requirements.txt (line 11))
  Using cached typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)
Collecting anyio<5,>=3.6.2 (from starlette<0.47.0,>=0.40.0->fastapi->-r requirements.txt (line 11))
  Using cached anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)
Collecting idna>=2.8 (from anyio<5,>=3.6.2->starlette<0.47.0,>=0.40.0->fastapi->-r requirements.txt (line 11))
  Using cached idna-3.10-py3-none-any.whl.metadata (10 kB)
Collecting sniffio>=1.1 (from anyio<5,>=3.6.2->starlette<0.47.0,>=0.40.0->fastapi->-r requirements.txt (line 11))
  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting click>=7.0 (from uvicorn[standard]->-r requirements.txt (line 12))
  Using cached click-8.2.1-py3-none-any.whl.metadata (2.5 kB)
Collecting h11>=0.8 (from uvicorn[standard]->-r requirements.txt (line 12))
  Using cached h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Collecting httptools>=0.6.3 (from uvicorn[standard]->-r requirements.txt (line 12))
  Downloading httptools-0.6.4-cp311-cp311-macosx_11_0_arm64.whl.metadata (3.6 kB)
Collecting python-dotenv>=0.13 (from uvicorn[standard]->-r requirements.txt (line 12))
  Using cached python_dotenv-1.1.0-py3-none-any.whl.metadata (24 kB)
Collecting pyyaml>=5.1 (from uvicorn[standard]->-r requirements.txt (line 12))
  Using cached PyYAML-6.0.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (2.1 kB)
Collecting uvloop>=0.15.1 (from uvicorn[standard]->-r requirements.txt (line 12))
  Downloading uvloop-0.21.0-cp311-cp311-macosx_10_9_universal2.whl.metadata (4.9 kB)
Collecting watchfiles>=0.13 (from uvicorn[standard]->-r requirements.txt (line 12))
  Downloading watchfiles-1.0.5-cp311-cp311-macosx_11_0_arm64.whl.metadata (4.9 kB)
Collecting iniconfig>=1 (from pytest->-r requirements.txt (line 14))
  Downloading iniconfig-2.1.0-py3-none-any.whl.metadata (2.7 kB)
Collecting packaging>=20 (from pytest->-r requirements.txt (line 14))
  Using cached packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
Collecting pluggy<2,>=1.5 (from pytest->-r requirements.txt (line 14))
  Downloading pluggy-1.6.0-py3-none-any.whl.metadata (4.8 kB)
Collecting pygments>=2.7.2 (from pytest->-r requirements.txt (line 14))
  Using cached pygments-2.19.1-py3-none-any.whl.metadata (2.5 kB)
Collecting coverage>=7.5 (from coverage[toml]>=7.5->pytest-cov->-r requirements.txt (line 15))
  Downloading coverage-7.8.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (8.9 kB)
Collecting mypy-extensions>=0.4.3 (from black->-r requirements.txt (line 18))
  Downloading mypy_extensions-1.1.0-py3-none-any.whl.metadata (1.1 kB)
Collecting pathspec>=0.9.0 (from black->-r requirements.txt (line 18))
  Using cached pathspec-0.12.1-py3-none-any.whl.metadata (21 kB)
Collecting platformdirs>=2 (from black->-r requirements.txt (line 18))
  Downloading platformdirs-4.3.8-py3-none-any.whl.metadata (12 kB)
Collecting snowballstemmer>=2.2.0 (from pydocstyle->-r requirements.txt (line 19))
  Downloading snowballstemmer-3.0.1-py3-none-any.whl.metadata (7.9 kB)
Collecting cfgv>=2.0.0 (from pre-commit->-r requirements.txt (line 20))
  Downloading cfgv-3.4.0-py2.py3-none-any.whl.metadata (8.5 kB)
Collecting identify>=1.0.0 (from pre-commit->-r requirements.txt (line 20))
  Downloading identify-2.6.12-py2.py3-none-any.whl.metadata (4.4 kB)
Collecting nodeenv>=0.11.1 (from pre-commit->-r requirements.txt (line 20))
  Downloading nodeenv-1.9.1-py2.py3-none-any.whl.metadata (21 kB)
Collecting virtualenv>=20.10.0 (from pre-commit->-r requirements.txt (line 20))
  Downloading virtualenv-20.31.2-py3-none-any.whl.metadata (4.5 kB)
Collecting mpmath<1.4,>=1.1.0 (from sympy>=1.13.3->torch->-r requirements.txt (line 7))
  Using cached mpmath-1.3.0-py3-none-any.whl.metadata (8.6 kB)
Collecting distlib<1,>=0.3.7 (from virtualenv>=20.10.0->pre-commit->-r requirements.txt (line 20))
  Downloading distlib-0.3.9-py2.py3-none-any.whl.metadata (5.2 kB)
Collecting MarkupSafe>=2.0 (from jinja2->torch->-r requirements.txt (line 7))
  Using cached MarkupSafe-3.0.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (4.0 kB)
Collecting contourpy>=1.0.1 (from matplotlib->filterpy->-r requirements.txt (line 5))
  Using cached contourpy-1.3.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (5.5 kB)
Collecting cycler>=0.10 (from matplotlib->filterpy->-r requirements.txt (line 5))
  Using cached cycler-0.12.1-py3-none-any.whl.metadata (3.8 kB)
Collecting fonttools>=4.22.0 (from matplotlib->filterpy->-r requirements.txt (line 5))
  Using cached fonttools-4.58.1-cp311-cp311-macosx_10_9_universal2.whl.metadata (106 kB)
Collecting kiwisolver>=1.3.1 (from matplotlib->filterpy->-r requirements.txt (line 5))
  Using cached kiwisolver-1.4.8-cp311-cp311-macosx_11_0_arm64.whl.metadata (6.2 kB)
Collecting pyparsing>=2.3.1 (from matplotlib->filterpy->-r requirements.txt (line 5))
  Using cached pyparsing-3.2.3-py3-none-any.whl.metadata (5.0 kB)
Collecting python-dateutil>=2.7 (from matplotlib->filterpy->-r requirements.txt (line 5))
  Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Collecting six>=1.5 (from python-dateutil>=2.7->matplotlib->filterpy->-r requirements.txt (line 5))
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Using cached numpy-2.2.6-cp311-cp311-macosx_14_0_arm64.whl (5.4 MB)
Using cached websockets-15.0.1-cp311-cp311-macosx_11_0_arm64.whl (173 kB)
Using cached opencv_python-*********-cp37-abi3-macosx_13_0_arm64.whl (37.3 MB)
Using cached scipy-1.15.3-cp311-cp311-macosx_14_0_arm64.whl (22.4 MB)
Downloading torch-2.7.1-cp311-none-macosx_11_0_arm64.whl (68.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 68.6/68.6 MB 9.8 MB/s eta 0:00:00
Downloading torchvision-0.22.1-cp311-cp311-macosx_11_0_arm64.whl (1.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.9/1.9 MB 9.0 MB/s eta 0:00:00
Using cached pillow-11.2.1-cp311-cp311-macosx_11_0_arm64.whl (3.0 MB)
Using cached fastapi-0.115.12-py3-none-any.whl (95 kB)
Using cached pydantic-2.11.5-py3-none-any.whl (444 kB)
Using cached pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl (1.9 MB)
Using cached starlette-0.46.2-py3-none-any.whl (72 kB)
Using cached anyio-4.9.0-py3-none-any.whl (100 kB)
Using cached uvicorn-0.34.3-py3-none-any.whl (62 kB)
Downloading pytest-8.4.0-py3-none-any.whl (363 kB)
Downloading pluggy-1.6.0-py3-none-any.whl (20 kB)
Downloading pytest_cov-6.1.1-py3-none-any.whl (23 kB)
Downloading pytest_asyncio-1.0.0-py3-none-any.whl (15 kB)
Downloading ruff-0.11.13-py3-none-macosx_11_0_arm64.whl (10.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 10.4/10.4 MB 9.9 MB/s eta 0:00:00
Using cached black-25.1.0-cp311-cp311-macosx_11_0_arm64.whl (1.4 MB)
Downloading pydocstyle-6.3.0-py3-none-any.whl (38 kB)
Downloading pre_commit-4.2.0-py2.py3-none-any.whl (220 kB)
Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading cfgv-3.4.0-py2.py3-none-any.whl (7.2 kB)
Using cached click-8.2.1-py3-none-any.whl (102 kB)
Downloading coverage-7.8.2-cp311-cp311-macosx_11_0_arm64.whl (212 kB)
Using cached h11-0.16.0-py3-none-any.whl (37 kB)
Downloading httptools-0.6.4-cp311-cp311-macosx_11_0_arm64.whl (103 kB)
Downloading identify-2.6.12-py2.py3-none-any.whl (99 kB)
Using cached idna-3.10-py3-none-any.whl (70 kB)
Downloading iniconfig-2.1.0-py3-none-any.whl (6.0 kB)
Downloading mypy_extensions-1.1.0-py3-none-any.whl (5.0 kB)
Downloading nodeenv-1.9.1-py2.py3-none-any.whl (22 kB)
Using cached packaging-25.0-py3-none-any.whl (66 kB)
Using cached pathspec-0.12.1-py3-none-any.whl (31 kB)
Downloading platformdirs-4.3.8-py3-none-any.whl (18 kB)
Using cached pygments-2.19.1-py3-none-any.whl (1.2 MB)
Using cached python_dotenv-1.1.0-py3-none-any.whl (20 kB)
Using cached PyYAML-6.0.2-cp311-cp311-macosx_11_0_arm64.whl (172 kB)
Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading snowballstemmer-3.0.1-py3-none-any.whl (103 kB)
Using cached sympy-1.14.0-py3-none-any.whl (6.3 MB)
Using cached mpmath-1.3.0-py3-none-any.whl (536 kB)
Using cached typing_extensions-4.14.0-py3-none-any.whl (43 kB)
Using cached typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Downloading uvloop-0.21.0-cp311-cp311-macosx_10_9_universal2.whl (1.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.4/1.4 MB 5.4 MB/s eta 0:00:00
Downloading virtualenv-20.31.2-py3-none-any.whl (6.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 6.1/6.1 MB 9.8 MB/s eta 0:00:00
Downloading distlib-0.3.9-py2.py3-none-any.whl (468 kB)
Using cached filelock-3.18.0-py3-none-any.whl (16 kB)
Downloading watchfiles-1.0.5-cp311-cp311-macosx_11_0_arm64.whl (395 kB)
Using cached fsspec-2025.5.1-py3-none-any.whl (199 kB)
Using cached jinja2-3.1.6-py3-none-any.whl (134 kB)
Using cached MarkupSafe-3.0.2-cp311-cp311-macosx_11_0_arm64.whl (12 kB)
Using cached matplotlib-3.10.3-cp311-cp311-macosx_11_0_arm64.whl (8.1 MB)
Using cached contourpy-1.3.2-cp311-cp311-macosx_11_0_arm64.whl (254 kB)
Using cached cycler-0.12.1-py3-none-any.whl (8.3 kB)
Using cached fonttools-4.58.1-cp311-cp311-macosx_10_9_universal2.whl (2.8 MB)
Using cached kiwisolver-1.4.8-cp311-cp311-macosx_11_0_arm64.whl (65 kB)
Using cached pyparsing-3.2.3-py3-none-any.whl (111 kB)
Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Using cached networkx-3.5-py3-none-any.whl (2.0 MB)
Installing collected packages: mpmath, distlib, websockets, uvloop, typing-extensions, sympy, snowballstemmer, sniffio, six, ruff, pyyaml, python-dotenv, pyparsing, pygments, pluggy, platformdirs, Pillow, pathspec, packaging, numpy, nodeenv, networkx, mypy-extensions, MarkupSafe, kiwisolver, iniconfig, idna, identify, httptools, h11, fsspec, fonttools, filelock, cycler, coverage, click, cfgv, annotated-types, virtualenv, uvicorn, typing-inspection, scipy, python-dateutil, pytest, pydocstyle, pydantic-core, opencv-python, jinja2, contourpy, black, anyio, watchfiles, torch, starlette, pytest-cov, pytest-asyncio, pydantic, pre-commit, matplotlib, torchvision, filterpy, fastapi

Successfully installed MarkupSafe-3.0.2 Pillow-11.2.1 annotated-types-0.7.0 anyio-4.9.0 black-25.1.0 cfgv-3.4.0 click-8.2.1 contourpy-1.3.2 coverage-7.8.2 cycler-0.12.1 distlib-0.3.9 fastapi-0.115.12 filelock-3.18.0 filterpy-1.4.5 fonttools-4.58.1 fsspec-2025.5.1 h11-0.16.0 httptools-0.6.4 identify-2.6.12 idna-3.10 iniconfig-2.1.0 jinja2-3.1.6 kiwisolver-1.4.8 matplotlib-3.10.3 mpmath-1.3.0 mypy-extensions-1.1.0 networkx-3.5 nodeenv-1.9.1 numpy-2.2.6 opencv-python-********* packaging-25.0 pathspec-0.12.1 platformdirs-4.3.8 pluggy-1.6.0 pre-commit-4.2.0 pydantic-2.11.5 pydantic-core-2.33.2 pydocstyle-6.3.0 pygments-2.19.1 pyparsing-3.2.3 pytest-8.4.0 pytest-asyncio-1.0.0 pytest-cov-6.1.1 python-dateutil-2.9.0.post0 python-dotenv-1.1.0 pyyaml-6.0.2 ruff-0.11.13 scipy-1.15.3 six-1.17.0 sniffio-1.3.1 snowballstemmer-3.0.1 starlette-0.46.2 sympy-1.14.0 torch-2.7.1 torchvision-0.22.1 typing-extensions-4.14.0 typing-inspection-0.4.1 uvicorn-0.34.3 uvloop-0.21.0 virtualenv-20.31.2 watchfiles-1.0.5 websockets-15.0.1
✅ 0.4  Writing all source files…
pre-commit installed at .git/hooks/pre-commit
[INFO] Initializing environment for https://github.com/astral-sh/ruff-pre-commit.
[INFO] Initializing environment for https://github.com/psf/black.
[INFO] Initializing environment for https://github.com/PyCQA/pydocstyle.
[INFO] Installing environment for https://github.com/astral-sh/ruff-pre-commit.
[INFO] Once installed this environment will be reused.
[INFO] This may take a few minutes...
[INFO] Installing environment for https://github.com/psf/black.
[INFO] Once installed this environment will be reused.
[INFO] This may take a few minutes...
[INFO] Installing environment for https://github.com/PyCQA/pydocstyle.
[INFO] Once installed this environment will be reused.
[INFO] This may take a few minutes...
ruff.....................................................................Failed
- hook id: ruff
- exit code: 2

ruff failed
  Cause: Failed to parse /Users/<USER>/Downloads/visionangel_pod_bundle/visionangel_pod_bundle/pyproject.toml
  Cause: TOML parse error at line 1, column 13
  |
1 | [tool.ruff] line-length = 120
  |             ^
invalid table header
expected newline, `#`

ruff failed
  Cause: Failed to parse /Users/<USER>/Downloads/visionangel_pod_bundle/visionangel_pod_bundle/pyproject.toml
  Cause: TOML parse error at line 1, column 13
  |
1 | [tool.ruff] line-length = 120
  |             ^
invalid table header
expected newline, `#`

ruff failed
  Cause: Failed to parse /Users/<USER>/Downloads/visionangel_pod_bundle/visionangel_pod_bundle/pyproject.toml
  Cause: TOML parse error at line 1, column 13
  |
1 | [tool.ruff] line-length = 120
  |             ^
invalid table header
expected newline, `#`

ruff failed
  Cause: Failed to parse /Users/<USER>/Downloads/visionangel_pod_bundle/visionangel_pod_bundle/pyproject.toml
  Cause: TOML parse error at line 1, column 13
  |
1 | [tool.ruff] line-length = 120
  |             ^
invalid table header
expected newline, `#`

ruff failed
  Cause: Failed to parse /Users/<USER>/Downloads/visionangel_pod_bundle/visionangel_pod_bundle/pyproject.toml
  Cause: TOML parse error at line 1, column 13
  |
1 | [tool.ruff] line-length = 120
  |             ^
invalid table header
expected newline, `#`

black....................................................................Failed
- hook id: black
- exit code: 1

Error: Could not open file '/Users/<USER>/Downloads/visionangel_pod_bundle/visionangel_pod_bundle/pyproject.toml': Error reading configuration file: Expected newline or end of document after a statement (at line 1, column 13)
Error: Could not open file '/Users/<USER>/Downloads/visionangel_pod_bundle/visionangel_pod_bundle/pyproject.toml': Error reading configuration file: Expected newline or end of document after a statement (at line 1, column 13)
Error: Could not open file '/Users/<USER>/Downloads/visionangel_pod_bundle/visionangel_pod_bundle/pyproject.toml': Error reading configuration file: Expected newline or end of document after a statement (at line 1, column 13)
Error: Could not open file '/Users/<USER>/Downloads/visionangel_pod_bundle/visionangel_pod_bundle/pyproject.toml': Error reading configuration file: Expected newline or end of document after a statement (at line 1, column 13)
Error: Could not open file '/Users/<USER>/Downloads/visionangel_pod_bundle/visionangel_pod_bundle/pyproject.toml': Error reading configuration file: Expected newline or end of document after a statement (at line 1, column 13)

pydocstyle...............................................................Failed
- hook id: pydocstyle
- exit code: 1

Traceback (most recent call last):
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/bin/pydocstyle", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 79, in main
    sys.exit(run_pydocstyle())
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 41, in run_pydocstyle
    for (
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 309, in get_files_to_check
    config = self._get_config(os.path.abspath(name))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 340, in _get_config_by_discovery
    config_file = self._get_config_file_in_folder(path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 599, in _get_config_file_in_folder
    if config.read(full_path) and cls._get_section_name(config):
       ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 75, in read
    self._config.update(tomllib.load(fp))
                        ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 66, in load
    return loads(s, parse_float=parse_float)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 127, in loads
    raise suffixed_err(
tomllib.TOMLDecodeError: Expected newline or end of document after a statement (at line 1, column 13)
Traceback (most recent call last):
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/bin/pydocstyle", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 79, in main
    sys.exit(run_pydocstyle())
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 41, in run_pydocstyle
    for (
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 309, in get_files_to_check
    config = self._get_config(os.path.abspath(name))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 340, in _get_config_by_discovery
    config_file = self._get_config_file_in_folder(path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 599, in _get_config_file_in_folder
    if config.read(full_path) and cls._get_section_name(config):
       ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 75, in read
    self._config.update(tomllib.load(fp))
                        ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 66, in load
    return loads(s, parse_float=parse_float)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 127, in loads
    raise suffixed_err(
tomllib.TOMLDecodeError: Expected newline or end of document after a statement (at line 1, column 13)
Traceback (most recent call last):
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/bin/pydocstyle", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 79, in main
    sys.exit(run_pydocstyle())
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 41, in run_pydocstyle
    for (
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 309, in get_files_to_check
    config = self._get_config(os.path.abspath(name))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 340, in _get_config_by_discovery
    config_file = self._get_config_file_in_folder(path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 599, in _get_config_file_in_folder
    if config.read(full_path) and cls._get_section_name(config):
       ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 75, in read
    self._config.update(tomllib.load(fp))
                        ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 66, in load
    return loads(s, parse_float=parse_float)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 127, in loads
    raise suffixed_err(
tomllib.TOMLDecodeError: Expected newline or end of document after a statement (at line 1, column 13)
Traceback (most recent call last):
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/bin/pydocstyle", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 79, in main
    sys.exit(run_pydocstyle())
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 41, in run_pydocstyle
    for (
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 309, in get_files_to_check
    config = self._get_config(os.path.abspath(name))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 340, in _get_config_by_discovery
    config_file = self._get_config_file_in_folder(path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 599, in _get_config_file_in_folder
    if config.read(full_path) and cls._get_section_name(config):
       ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 75, in read
    self._config.update(tomllib.load(fp))
                        ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 66, in load
    return loads(s, parse_float=parse_float)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 127, in loads
    raise suffixed_err(
tomllib.TOMLDecodeError: Expected newline or end of document after a statement (at line 1, column 13)
Traceback (most recent call last):
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/bin/pydocstyle", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 79, in main
    sys.exit(run_pydocstyle())
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 41, in run_pydocstyle
    for (
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 309, in get_files_to_check
    config = self._get_config(os.path.abspath(name))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 340, in _get_config_by_discovery
    config_file = self._get_config_file_in_folder(path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 599, in _get_config_file_in_folder
    if config.read(full_path) and cls._get_section_name(config):
       ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 75, in read
    self._config.update(tomllib.load(fp))
                        ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 66, in load
    return loads(s, parse_float=parse_float)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 127, in loads
    raise suffixed_err(
tomllib.TOMLDecodeError: Expected newline or end of document after a statement (at line 1, column 13)
Traceback (most recent call last):
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/bin/pydocstyle", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 79, in main
    sys.exit(run_pydocstyle())
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 41, in run_pydocstyle
    for (
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 309, in get_files_to_check
    config = self._get_config(os.path.abspath(name))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 340, in _get_config_by_discovery
    config_file = self._get_config_file_in_folder(path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 599, in _get_config_file_in_folder
    if config.read(full_path) and cls._get_section_name(config):
       ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 75, in read
    self._config.update(tomllib.load(fp))
                        ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 66, in load
    return loads(s, parse_float=parse_float)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 127, in loads
    raise suffixed_err(
tomllib.TOMLDecodeError: Expected newline or end of document after a statement (at line 1, column 13)
Traceback (most recent call last):
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/bin/pydocstyle", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 79, in main
    sys.exit(run_pydocstyle())
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 41, in run_pydocstyle
    for (
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 309, in get_files_to_check
    config = self._get_config(os.path.abspath(name))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 340, in _get_config_by_discovery
    config_file = self._get_config_file_in_folder(path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 599, in _get_config_file_in_folder
    if config.read(full_path) and cls._get_section_name(config):
       ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 75, in read
    self._config.update(tomllib.load(fp))
                        ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 66, in load
    return loads(s, parse_float=parse_float)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 127, in loads
    raise suffixed_err(
tomllib.TOMLDecodeError: Expected newline or end of document after a statement (at line 1, column 13)
Traceback (most recent call last):
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/bin/pydocstyle", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 79, in main
    sys.exit(run_pydocstyle())
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 41, in run_pydocstyle
    for (
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 309, in get_files_to_check
    config = self._get_config(os.path.abspath(name))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 340, in _get_config_by_discovery
    config_file = self._get_config_file_in_folder(path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 599, in _get_config_file_in_folder
    if config.read(full_path) and cls._get_section_name(config):
       ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 75, in read
    self._config.update(tomllib.load(fp))
                        ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 66, in load
    return loads(s, parse_float=parse_float)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 127, in loads
    raise suffixed_err(
tomllib.TOMLDecodeError: Expected newline or end of document after a statement (at line 1, column 13)
Traceback (most recent call last):
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/bin/pydocstyle", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 79, in main
    sys.exit(run_pydocstyle())
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 41, in run_pydocstyle
    for (
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 309, in get_files_to_check
    config = self._get_config(os.path.abspath(name))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 340, in _get_config_by_discovery
    config_file = self._get_config_file_in_folder(path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 599, in _get_config_file_in_folder
    if config.read(full_path) and cls._get_section_name(config):
       ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 75, in read
    self._config.update(tomllib.load(fp))
                        ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 66, in load
    return loads(s, parse_float=parse_float)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 127, in loads
    raise suffixed_err(
tomllib.TOMLDecodeError: Expected newline or end of document after a statement (at line 1, column 13)
Traceback (most recent call last):
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/bin/pydocstyle", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 79, in main
    sys.exit(run_pydocstyle())
             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/cli.py", line 41, in run_pydocstyle
    for (
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 309, in get_files_to_check
    config = self._get_config(os.path.abspath(name))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 346, in _get_config_by_discovery
    config = self._get_config(parent_dir)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 397, in _get_config
    config = self._get_config_by_discovery(node)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 340, in _get_config_by_discovery
    config_file = self._get_config_file_in_folder(path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 599, in _get_config_file_in_folder
    if config.read(full_path) and cls._get_section_name(config):
       ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.cache/pre-commit/repou2v_n4yr/py_env-python3.11/lib/python3.11/site-packages/pydocstyle/config.py", line 75, in read
    self._config.update(tomllib.load(fp))
                        ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 66, in load
    return loads(s, parse_float=parse_float)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tomllib/_parser.py", line 127, in loads
    raise suffixed_err(
tomllib.TOMLDecodeError: Expected newline or end of document after a statement (at line 1, column 13)

