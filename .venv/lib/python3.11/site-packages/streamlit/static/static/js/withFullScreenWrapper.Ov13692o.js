import{n as p,r as n,aE as x,aD as h,bh as y,z as C,bi as w,j as i,a_ as b}from"./index.C1z8KpLA.js";const F=p("div",{target:"e1q5ojhd0"})(({theme:e,isExpanded:t})=>({width:"100%",...t?{position:"fixed",top:0,left:0,bottom:0,right:0,background:e.colors.bgColor,zIndex:e.zIndices.fullscreenWrapper,padding:e.spacing.md,paddingTop:e.sizes.fullScreenHeaderHeight,overflow:"auto",display:"flex",alignItems:"center",justifyContent:"center"}:{}})),f=n.createContext(null);f.displayName="ElementFullscreenContext";const S=()=>{const{setFullScreen:e}=n.useContext(x),[t,c]=n.useState(!1),{fullHeight:s,fullWidth:u}=h(y),l=n.useCallback(r=>{c(r),e(r)},[e]),d=n.useCallback(()=>{document.body.style.overflow="hidden",l(!0)},[l]),o=n.useCallback(()=>{document.body.style.overflow="unset",l(!1)},[l]),a=n.useCallback(r=>{r.keyCode===27&&t&&o()},[o,t]);return n.useEffect(()=>(document.addEventListener("keydown",a,!1),()=>{document.removeEventListener("keydown",a,!1)}),[a]),n.useMemo(()=>({expanded:t,zoomIn:d,zoomOut:o,fullHeight:s,fullWidth:u}),[t,d,o,s,u])},g=({children:e,height:t})=>{const c=C(),{expanded:s,fullHeight:u,fullWidth:l,zoomIn:d,zoomOut:o}=S(),[a,r]=w(),m=n.useMemo(()=>({width:s?l:a,height:s?u:t,expanded:s,expand:d,collapse:o}),[s,u,l,t,a,d,o]);return i(f.Provider,{value:m,children:i(F,{ref:r,isExpanded:s,"data-testid":"stFullScreenFrame",theme:c,children:e})})};function v(e){const t=c=>i(g,{children:i(e,{...c})});return t.displayName=`withFullScreenWrapper(${e.displayName||e.name})`,b(t,e)}export{f as E,v as w};
