import{r as o,ct as i}from"./index.C1z8KpLA.js";import{i as c}from"./inputUtils.CQWz5UKz.js";function E(r,t,s,n){o.useEffect(()=>{n||r!==t&&s(r)},[r,t,n,s])}function l(r,t,s,n,u,f=!1){return o.useCallback(e=>{const a=f?e.metaKey||e.ctrlKey:!0;!c(e)||!a||(e.preventDefault(),s&&t(),n.allowFormEnterToSubmit(r)&&n.submitForm(r,u))},[r,u,s,t,n,f])}function F({formId:r,maxChars:t,setDirty:s,setUiValue:n,setValueWithSource:u}){return o.useCallback(f=>{const{value:e}=f.target;t!==0&&e.length>t||(s(!0),n(e),i({formId:r})&&u({value:e,fromUi:!0}))},[r,t,s,n,u])}export{F as a,l as b,E as u};
