# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/MetricsEvent.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import PageProfile_pb2 as streamlit_dot_proto_dot_PageProfile__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"streamlit/proto/MetricsEvent.proto\x1a!streamlit/proto/PageProfile.proto\"\xbf\x07\n\x0cMetricsEvent\x12\r\n\x05\x65vent\x18\x01 \x01(\t\x12\x14\n\x0c\x61nonymous_id\x18\x02 \x01(\t\x12\x15\n\rmachine_id_v3\x18\x03 \x01(\t\x12\x15\n\rmachine_id_v4\x18) \x01(\t\x12\x13\n\x0breport_hash\x18\x04 \x01(\t\x12\x0b\n\x03\x64\x65v\x18\x05 \x01(\x08\x12\x0e\n\x06source\x18\x06 \x01(\t\x12\x19\n\x11streamlit_version\x18\x07 \x01(\t\x12\x10\n\x08is_hello\x18\x08 \x01(\x08\x12\x0e\n\x06\x61pp_id\x18! \x01(\t\x12\x12\n\nsession_id\x18# \x01(\t\x12\x16\n\x0epython_version\x18$ \x01(\t\x12\x11\n\tserver_os\x18* \x01(\t\x12\x13\n\x0bhas_display\x18+ \x01(\x08\x12\x14\n\x0cis_webdriver\x18, \x01(\x08\x12\x11\n\thosted_at\x18\t \x01(\t\x12\r\n\x05owner\x18\n \x01(\t\x12\x0c\n\x04repo\x18\x0b \x01(\t\x12\x0e\n\x06\x62ranch\x18\x0c \x01(\t\x12\x13\n\x0bmain_module\x18\r \x01(\t\x12\x12\n\ncreator_id\x18\x0e \x01(\t\x12\x18\n\x10\x63ontext_page_url\x18\x0f \x01(\t\x12\x1a\n\x12\x63ontext_page_title\x18\x10 \x01(\t\x12\x19\n\x11\x63ontext_page_path\x18\x11 \x01(\t\x12\x1d\n\x15\x63ontext_page_referrer\x18\x12 \x01(\t\x12\x1b\n\x13\x63ontext_page_search\x18\x13 \x01(\t\x12\x16\n\x0e\x63ontext_locale\x18\x14 \x01(\t\x12\x1a\n\x12\x63ontext_user_agent\x18\x15 \x01(\t\x12\r\n\x05label\x18\x16 \x01(\t\x12\x1a\n\x08\x63ommands\x18\x17 \x03(\x0b\x32\x08.Command\x12\x11\n\texec_time\x18\x18 \x01(\x03\x12\x11\n\tprep_time\x18\x19 \x01(\x03\x12\x0e\n\x06\x63onfig\x18\x1a \x03(\t\x12\x1a\n\x12uncaught_exception\x18\x1b \x01(\t\x12\x14\n\x0c\x61ttributions\x18\x1c \x03(\t\x12\n\n\x02os\x18\x1d \x01(\t\x12\x10\n\x08timezone\x18\x1e \x01(\t\x12\x10\n\x08headless\x18\x1f \x01(\x08\x12\x17\n\x0fis_fragment_run\x18  \x01(\x08\x12\x10\n\x08numPages\x18\" \x01(\x03\x12\x18\n\x10page_script_hash\x18% \x01(\t\x12\x14\n\x0c\x61\x63tive_theme\x18& \x01(\t\x12\x17\n\x0ftotal_load_time\x18\' \x01(\x03\x12\"\n\x0c\x62rowser_info\x18( \x01(\x0b\x32\x0c.BrowserInfo\"]\n\x0b\x42rowserInfo\x12\x14\n\x0c\x62rowser_name\x18\x01 \x01(\t\x12\x17\n\x0f\x62rowser_version\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65vice_type\x18\x03 \x01(\t\x12\n\n\x02os\x18\x04 \x01(\tB1\n\x1c\x63om.snowflake.apps.streamlitB\x11MetricsEventProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.MetricsEvent_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\021MetricsEventProto'
  _globals['_METRICSEVENT']._serialized_start=74
  _globals['_METRICSEVENT']._serialized_end=1033
  _globals['_BROWSERINFO']._serialized_start=1035
  _globals['_BROWSERINFO']._serialized_end=1128
# @@protoc_insertion_point(module_scope)
