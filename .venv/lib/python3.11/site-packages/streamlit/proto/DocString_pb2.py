# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/DocString.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fstreamlit/proto/DocString.proto\"v\n\tDocString\x12\x12\n\ndoc_string\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x0c\n\x04name\x18\x06 \x01(\t\x12\r\n\x05value\x18\x07 \x01(\t\x12\x18\n\x07members\x18\x08 \x03(\x0b\x32\x07.MemberJ\x04\x08\x01\x10\x02J\x04\x08\x02\x10\x03J\x04\x08\x05\x10\x06\"W\n\x06Member\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x0f\n\x05value\x18\x03 \x01(\tH\x00\x12\x14\n\ndoc_string\x18\x04 \x01(\tH\x00\x42\n\n\x08\x63ontentsB.\n\x1c\x63om.snowflake.apps.streamlitB\x0e\x44ocStringProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.DocString_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\016DocStringProto'
  _globals['_DOCSTRING']._serialized_start=35
  _globals['_DOCSTRING']._serialized_end=153
  _globals['_MEMBER']._serialized_start=155
  _globals['_MEMBER']._serialized_end=242
# @@protoc_insertion_point(module_scope)
