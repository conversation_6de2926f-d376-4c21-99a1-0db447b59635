# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/RootContainer.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#streamlit/proto/RootContainer.proto*=\n\rRootContainer\x12\x08\n\x04MAIN\x10\x00\x12\x0b\n\x07SIDEBAR\x10\x01\x12\t\n\x05\x45VENT\x10\x02\x12\n\n\x06\x42OTTOM\x10\x03\x42\x32\n\x1c\x63om.snowflake.apps.streamlitB\x12RootContainerProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.RootContainer_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\022RootContainerProto'
  _globals['_ROOTCONTAINER']._serialized_start=39
  _globals['_ROOTCONTAINER']._serialized_end=100
# @@protoc_insertion_point(module_scope)
